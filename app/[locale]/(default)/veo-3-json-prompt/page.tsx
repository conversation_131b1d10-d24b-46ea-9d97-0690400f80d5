import { getTranslations } from "next-intl/server";
import Veo3JsonGenerator from "@/components/ui/veo-3-json-generator";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/veo-3-json-prompt`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/veo-3-json-prompt`;
  }

  const t = await getTranslations("veo3_json_generator");

  return {
    title: t("title"),
    description: t("description"),
    keywords: "veo-3, json prompt, video generation, ai prompt generator",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function Veo3JsonPromptPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations("veo3_json_generator");

  const config = {
    title: t("title"),
    subtitle: t("subtitle"),
    templateMode: t("templateMode"),
    aiMode: t("aiMode"),
    selectTemplate: t("selectTemplate"),
    generateJson: t("generateJson"),
    copyJson: t("copyJson"),
    inputPrompt: t("inputPrompt"),
    generateButton: t("generateButton"),
    generating: t("generating"),
    jsonPreview: t("jsonPreview"),
    sampleVideo: t("sampleVideo"),
    successCopied: t("successCopied"),
    errorCopyFailed: t("errorCopyFailed"),
    errorNoInput: t("errorNoInput"),
    errorGenerateFailed: t("errorGenerateFailed"),
    successGenerated: t("successGenerated"),
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">{config.title}</h1>
          <p className="text-xl text-muted-foreground">{config.subtitle}</p>
        </div>
        
        <Veo3JsonGenerator config={config} />
      </div>
    </div>
  );
}
