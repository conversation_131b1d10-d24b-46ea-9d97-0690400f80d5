import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import Tips from "@/components/blocks/tips";
import PromptGenerator from "@/components/prompt-generator";
import EnhancedPromptGenerator from "@/components/ui/enhanced-prompt-generator";
import { createCommonPromptConfig } from "@/lib/enhanced-prompt-configs";
import { getChatgptPromptGeneratorPage, getLandingPage } from "@/services/page";
import { getTranslations } from "next-intl/server";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/chatgpt-prompt-generator`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/chatgpt-prompt-generator`;
  }

  const page = await getChatgptPromptGeneratorPage(locale);

  return {
    title: page.title,
    description: page.description,
    keywords: "",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getChatgptPromptGeneratorPage(locale);
  const t = await getTranslations("chatgpt_prompt_generator");

  const veo3Config = createCommonPromptConfig({
    title: t("title"),
    subtitle: t("subtitle"),
    inputLabel: t("inputLabel"),
    inputPlaceholder: t("inputPlaceholder"),
    outputLabel: t("outputLabel"),
    outputPlaceholder: t("outputPlaceholder"),
    generateButton: t("generateButton"),
    generating: t("generating"),
    generatingText: t("generatingText"),
    waitingText: t("waitingText"),
    copyButton: t("copyButton"),
    favoriteButton: t("favoriteButton"),
    modeBasic: t("modeBasic"),
    modeAdvanced: t("modeAdvanced"),
    errorNoInput: t("errorNoInput"),
    errorGenerateFailed: t("errorGenerateFailed"),
    errorNoContent: t("errorNoContent"),
    errorCopyFailed: t("errorCopyFailed"),
    errorGetCategoriesFailed: t("errorGetCategoriesFailed"),
    errorGetCategoriesFailedWithMsg: t("errorGetCategoriesFailedWithMsg", { message: "{message}" }),
    errorFavoriteFailed: t("errorFavoriteFailed"),
    successGenerated: t("successGenerated"),
    successCopied: t("successCopied"),
    successFavoriteSaved: t("successFavoriteSaved"),
    modelType: "text",
    modelName: "chatgpt",
  });

  return (
    <>
      {page.hero && <Hero hero={page.hero} />}
      <EnhancedPromptGenerator 
        config={veo3Config} 
      />
      {page.branding && <Branding section={page.branding} />}
      {page.usage && <Feature3 section={page.usage} />}
      {/* {page.tips && <Tips section={page.tips} />} */}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.stats && <Stats section={page.stats} />}
      {/* {page.pricing && <Pricing pricing={page.pricing} />} */}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
