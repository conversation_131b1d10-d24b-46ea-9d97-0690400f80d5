import {
  LanguageModelV1,
  extractReasoningMiddleware,
  generateText,
  wrapLanguageModel,
} from "ai";
import { respData, respErr } from "@/lib/resp";

import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { getUserUuid } from "@/services/user";
import { CreditsAmount, CreditsTransType, decreaseCredits, getUserCredits } from "@/services/credit";
import { PromptOptimizerFilters } from "@/types/prompt-optimizer";
import { getPromptOptimizers } from "@/models/prompt-optimizer";

export async function POST(req: Request) {
  try {
    const { input, mode, model_type, model_name, locale } = await req.json();
    console.log(input, mode, model_type, model_name, locale);
    let model = "deepseek/deepseek-chat-v3-0324:free";
    let cost_credit = 0;

    if (!input || !model_type) {
      return respErr("invalid params");
    }
    
    let filter: PromptOptimizerFilters = {
      model_type: model_type,
      model_name: model_name,
      language: locale,
      status: "online",
      version: "1.0.0",
    };

    const prompt_optimizers = await getPromptOptimizers(filter);
    console.log("prompt_optimizers", prompt_optimizers);
    if (prompt_optimizers.length === 0) {
      return respErr("no prompt optimizer found");
    }
    const prompt_optimizer = prompt_optimizers[0];

    const user_uuid = await getUserUuid();
    if (user_uuid) {
      if (mode === "advanced") {
        cost_credit = 1;
        model = "openai/gpt-4.1";
        const credits = await getUserCredits(user_uuid);
        if (credits.left_credits < cost_credit) {
          return respErr("credits not enough, please buy credits");
        }
      }
    }

    let textModel: LanguageModelV1;
    let provider = "openrouter";
    let prompt = prompt_optimizer.prompt + "<" + input + ">";
    console.log(prompt);

    const openrouter = createOpenRouter({
      apiKey: process.env.OPENROUTER_API_KEY,
    });
    textModel = openrouter(model);

    if (model === "deepseek/deepseek-r1") {
      const enhancedModel = wrapLanguageModel({
        model: textModel,
        middleware: extractReasoningMiddleware({
          tagName: "think",
        }),
      });
      textModel = enhancedModel;
    }

    const { reasoning, text, warnings } = await generateText({
      model: textModel,
      prompt: prompt,
    });

    if (warnings && warnings.length > 0) {
      console.log("enhance prompt warnings:", provider, warnings);
      return respErr("enhance prompt failed");
    }

    if (cost_credit > 0) {
      await decreaseCredits({
        user_uuid: user_uuid,
        trans_type: CreditsTransType.Prompt_Generator,
        credits: CreditsAmount.PromptGeneratorCost,
      });
    }

    return respData({
      text: text,
      reasoning: reasoning,
      // left_credits: credits.left_credits,
    });
  } catch (err) {
    console.log("enhance prompt failed:", err);
    return respErr("enhance prompt failed");
  }
}
