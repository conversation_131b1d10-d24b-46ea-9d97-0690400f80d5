"use client";

import { useState } from "react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Veo3JsonGeneratorConfig, Veo3Mode } from "@/types/veo-3";
import TemplateMode from "./veo-3-template-mode";
import AiMode from "./veo-3-ai-mode";

interface Veo3JsonGeneratorProps {
  config: Veo3JsonGeneratorConfig;
}

export default function Veo3JsonGenerator({ config }: Veo3JsonGeneratorProps) {
  const [mode, setMode] = useState<Veo3Mode>("template");

  return (
    <div className="w-full">
      <Tabs value={mode} onValueChange={(value) => setMode(value as Veo3Mode)} className="w-full">
        <div className="flex justify-center mb-8">
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="template" className="flex items-center gap-2">
              {config.templateMode}
            </TabsTrigger>
            <TabsTrigger value="ai" className="flex items-center gap-2">
              {config.aiMode}
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="template" className="mt-0">
          <TemplateMode config={config} />
        </TabsContent>

        <TabsContent value="ai" className="mt-0">
          <AiMode config={config} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
